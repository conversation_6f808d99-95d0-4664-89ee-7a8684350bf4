import { and, assign, setup } from 'xstate'

import {
  changeProduct,
  storeBankingInfo,
  updateReturns,
  wipeBankFetchError,
} from './BankMachineActions'
import {
  isAuthenticated,
  isInvestmentAccountOpen,
  isLiteBuild,
} from './BankMachineGuards'
import { getReturns, readUserBankingInfo } from './BankMachineServices'
import { bankPromiseToPromiseActor } from './BankMachineUtils'
import { BankMachineContext, Events } from './types/BankMachineTypes.type'

const initialContextValues = {
  bankingInfo: {
    nominalBalance: [],
    payoutHistory: [],
    payinHistory: [],
    nextPayout: null,
    all: [],
  },
  bankingInfoError: undefined,
  tontineProduct: 'TontineTrustFund',
}

/**
 * Bank machine configuration
 */
export const bankMachine = setup({
  types: {
    context: {} as BankMachineContext,
    events: {} as Events,
  },
  actors: {
    readUserBankingInfo: bankPromiseToPromiseActor(readUserBankingInfo),
    getReturnsActor: bankPromiseToPromiseActor(getReturns),
  },
  actions: {
    updateReturns: assign(updateReturns),
    wipeBankFetchError: assign(wipeBankFetchError),
    changeProduct: assign(changeProduct),
    storeBankingInfo: assign(storeBankingInfo),
  },
  guards: {
    isLiteBuild,
    isAuthenticated,
    isInvestmentAccountOpen,
  },
}).createMachine({
  context: initialContextValues,
  id: 'BankMachine',
  initial: 'IDLE',
  states: {
    IDLE: {
      on: {
        UPDATE_PRODUCT: {
          actions: { type: 'changeProduct' },
        },
        FETCH_BANKING_INFO: {
          target: 'FETCHING_BANK_INFO',
        },
        GET_RETURNS: {
          target: 'GETTING_RETURNS',
        },
      },
    },
    GETTING_RETURNS: {
      invoke: {
        src: 'getReturnsActor',
        id: 'getReturnsActorID',
        input: ({
          context,
          event,
        }: { context: BankMachineContext; event: Events }) => ({
          context,
          event,
        }),
        onDone: [
          {
            guard: 'isLiteBuild',
            target: 'IDLE',
            actions: [{ type: 'updateReturns' }],
          },
          {
            guard: and([
              { type: 'isAuthenticated' },
              { type: 'isInvestmentAccountOpen' },
            ]),
            target: 'FETCHING_BANK_INFO',
            actions: [{ type: 'updateReturns' }],
          },
          {
            target: 'IDLE',
            actions: [{ type: 'updateReturns' }],
          },
        ],
        onError: {
          target: 'IDLE',
        },
      },
    },

    FETCHING_BANK_INFO: {
      invoke: {
        src: 'readUserBankingInfo',
        id: 'readUserBankingInfoID',
        input: ({
          context,
          event,
        }: { context: BankMachineContext; event: Events }) => ({
          context,
          event,
        }),
        onError: {
          target: 'IDLE',
        },
        onDone: {
          target: 'IDLE',
          actions: [
            { type: 'storeBankingInfo' },
            { type: 'wipeBankFetchError' },
          ],
        },
      },
      description:
        'Makes a parallel request to multiple BS APIs to fetch all banking information',
    },
  },
})
